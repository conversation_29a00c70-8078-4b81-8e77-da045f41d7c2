#!/bin/bash

echo "🔍 Docker Build Debug Script"
echo "============================"

# Function to test Docker build with different approaches
test_docker_build() {
    local dockerfile=$1
    local requirements=$2
    local tag=$3
    
    echo "🧪 Testing build with:"
    echo "   Dockerfile: $dockerfile"
    echo "   Requirements: $requirements"
    echo "   Tag: $tag"
    
    # Copy requirements file if different
    if [ "$requirements" != "requirements.txt" ]; then
        cp "$requirements" requirements.txt.backup
        cp "$requirements" requirements.txt
    fi
    
    # Try to build
    if docker build -f "$dockerfile" -t "$tag" . ; then
        echo "✅ Build successful with $dockerfile"
        return 0
    else
        echo "❌ Build failed with $dockerfile"
        return 1
    fi
}

# Test 1: Simple Dockerfile with minimal requirements
echo "🔧 Test 1: Simple Dockerfile with minimal requirements"
if test_docker_build "Dockerfile.simple" "requirements.minimal.txt" "rag-backend:test1"; then
    echo "✅ Test 1 passed - using simple approach"
    exit 0
fi

# Test 2: Original Dockerfile with minimal requirements  
echo "🔧 Test 2: Original Dockerfile with minimal requirements"
if test_docker_build "Dockerfile" "requirements.minimal.txt" "rag-backend:test2"; then
    echo "✅ Test 2 passed - issue was with requirements versions"
    exit 0
fi

# Test 3: Simple Dockerfile with full requirements
echo "🔧 Test 3: Simple Dockerfile with full requirements"
if test_docker_build "Dockerfile.simple" "requirements.txt" "rag-backend:test3"; then
    echo "✅ Test 3 passed - issue was with Dockerfile complexity"
    exit 0
fi

echo "❌ All tests failed. Manual debugging required."
echo ""
echo "💡 Debugging steps:"
echo "1. Check if Docker daemon is running"
echo "2. Try building with --no-cache flag"
echo "3. Check individual pip install commands"
echo "4. Verify network connectivity for pip downloads"
echo ""
echo "🔍 To debug manually:"
echo "   docker build --no-cache -t rag-backend:debug ."
echo "   docker run -it python:3.11-slim bash"
echo "   # Then manually run pip install commands"
