from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv # For loading environment variables
import os
import shutil
from typing import List
import traceback # For detailed error logs

# LangChain components
from langchain_community.document_loaders import UnstructuredFileLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQ<PERSON>
from langchain_core.prompts import PromptTemplate

# *** IMPORTANT: Using AZURE OpenAI for both LLM and Embeddings ***
# Make sure you have installed: pip install langchain-openai
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings

# *** IMPORTANT: Using AZURE AI Search for Vector Store ***
# Make sure you have installed: pip install azure-search-documents
from langchain_community.vectorstores import AzureSearch

# Load environment variables from .env file
load_dotenv()

app = FastAPI()

# --- CORS Configuration ---
origins = [
    "http://localhost:3001", # Your React app's default address
    # If you changed your React frontend to a different port (e.g., 3001), update this:
    # "http://localhost:3001",
    # Add other origins if your frontend is hosted elsewhere
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- RAG Setup Variables ---
UPLOAD_DIRECTORY = "uploaded_files"
# VECTOR_DB_DIRECTORY = "chroma_db" # No longer needed for Chroma persistence as we use Azure AI Search

# Global variables to hold the vector store and LLM instance
vectorstore = None
llm = None
embeddings_model = None # Store the embeddings model globally

# Ensure upload directory exists
os.makedirs(UPLOAD_DIRECTORY, exist_ok=True)


# --- Helper Functions for RAG Pipeline ---

async def initialize_llm_and_embeddings():
    """Initializes LLM and Embeddings if they are not already."""
    global llm, embeddings_model

    # Initialize LLM (AzureChatOpenAI) if not already done
    if llm is None:
        llm = AzureChatOpenAI(
            azure_deployment=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_CHAT_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_API_CHAT_KEY"),
            temperature=0.7 # Adjust for creativity (0.0 for deterministic, 1.0 for highly creative)
        )
        print(f"LLM initialized: {llm.model_name if hasattr(llm, 'model_name') else llm.__class__.__name__}")

    # Initialize Embeddings (AzureOpenAIEmbeddings) if not already done
    if embeddings_model is None:
        embeddings_model = AzureOpenAIEmbeddings(
            azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"), # Your embedding deployment name
            openai_api_version=os.getenv("AZURE_OPENAI_API_EMBEDDING_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
            chunk_size=16 # Recommended for Azure embeddings
        )
        print("Azure OpenAI Embedding model initialized.")

async def get_azure_ai_search_vectorstore():
    """Initializes or retrieves the Azure AI Search vector store."""
    global vectorstore, embeddings_model

    # Ensure embeddings_model is initialized before creating vectorstore
    # The vector store needs an embedding function to perform vector searches
    if embeddings_model is None:
        await initialize_llm_and_embeddings() # Ensure embeddings are ready

    # Initialize Azure AI Search Vector Store if not already done
    if vectorstore is None:
        print("Initializing Azure AI Search Vector Store...")
        try:
            vectorstore = AzureSearch(
                azure_search_endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
                azure_search_key=os.getenv("AZURE_AI_SEARCH_API_KEY"),
                index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
                # Pass the embed_query method as the embedding function
                embedding_function=embeddings_model.embed_query,
            )
            print(f"Azure AI Search Vector Store initialized with index '{os.getenv('AZURE_AI_SEARCH_INDEX_NAME')}'.")
        except Exception as e:
            print(f"Error initializing Azure AI Search Vector Store: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to connect to Azure AI Search: {e}")
    return vectorstore


async def process_and_store_document(file_path: str):
    print(f"Processing document: {file_path} for RAG...")

    # Ensure LLM and Embeddings are initialized before processing
    await initialize_llm_and_embeddings()

    # 1. Load Document
    loader = UnstructuredFileLoader(file_path)
    documents = loader.load()
    print(f"Loaded {len(documents)} pages/chunks from {file_path}")

    # 2. Split Document into Chunks
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    texts = text_splitter.split_documents(documents)
    print(f"Split into {len(texts)} text chunks.")

    # 3. Get the Azure AI Search Vector Store instance
    search_vectorstore = await get_azure_ai_search_vectorstore()

    # 4. Add documents to Azure AI Search
    print(f"Adding {len(texts)} chunks to Azure AI Search index '{os.getenv('AZURE_AI_SEARCH_INDEX_NAME')}'...")
    try:
        # AzureSearch has an add_documents method.
        # It automatically handles creating the index if it doesn't exist on the first add.
        search_vectorstore.add_documents(documents=texts)
        print("Documents successfully added to Azure AI Search.")
    except Exception as e:
        print(f"Error adding documents to Azure AI Search: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to add documents to Azure AI Search: {e}")

# --- API Endpoints ---

@app.get("/")
async def root():
    return {"message": "FastAPI server is running!"}

@app.post("/upload")
async def upload_file_endpoint(file: UploadFile = File(...)):
    """
    Handles file uploads, saves them, and then processes them for RAG.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file name provided.")

    file_location = os.path.join(UPLOAD_DIRECTORY, file.filename)

    try:
        # Save the file
        with open(file_location, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        print(f"File '{file.filename}' saved successfully to {file_location}")

        # --- Trigger RAG Processing after saving ---
        await process_and_store_document(file_location)

        return JSONResponse(status_code=200, content={"message": f"File '{file.filename}' uploaded and processed for RAG!"})
    except Exception as e:
        print(f"Error during file upload or RAG processing for '{file.filename}': {e}")
        # Print full traceback for detailed debugging in development
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to process file: {e}")
    finally:
        # Ensure the uploaded file is closed
        file.file.close()


@app.post("/query")
async def process_query_endpoint(data: dict):
    """
    Receives a user query, retrieves relevant information, and generates a response.
    """
    query = data.get("query")
    if not query:
        raise HTTPException(status_code=400, detail="Query not provided.")

    global llm, embeddings_model # Ensure these are accessible globally

    # Ensure LLM and Embeddings are initialized
    await initialize_llm_and_embeddings()

    # Get the Azure AI Search Vector Store instance
    search_vectorstore = await get_azure_ai_search_vectorstore()

    print(f"Received query: '{query}'. Retrieving relevant documents from Azure AI Search...")

    try:
        # Define a custom prompt to guide the LLM's response
        qa_template = """Use the following pieces of context to answer the question at the end.
        If you don't know the answer, just say that you don't know, don't try to make up an answer.
        Use three sentences maximum and keep the answer as concise as possible.
        Always say "Thanks for asking!" at the end of the answer.

        {context}

        Question: {question}
        Helpful Answer:"""
        QA_CHAIN_PROMPT = PromptTemplate.from_template(qa_template)

        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff", # "stuff" concatenates all retrieved documents into the prompt
            # AzureSearch implements the Retriever interface
            retriever=search_vectorstore.as_retriever(),
            return_source_documents=True, # Optional: to see which documents were used
            chain_type_kwargs={"prompt": QA_CHAIN_PROMPT} # Apply custom prompt
        )

        result = await qa_chain.ainvoke({"query": query}) # Use ainvoke for async operations

        response_text = result["result"]
        # print(f"Retrieved source documents: {result.get('source_documents')}") # For debugging purposes
        return JSONResponse(status_code=200, content={"response": response_text})

    except Exception as e:
        print(f"Error during query processing: {e}")
        traceback.print_exc() # Print full traceback for debugging
        # More specific error handling could be added here for different Azure AI Search errors
        return JSONResponse(status_code=500, content={"response": f"An error occurred during processing: {e}"})