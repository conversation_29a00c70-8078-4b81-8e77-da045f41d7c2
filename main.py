from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv # For loading environment variables
import os
import shutil
from typing import List
import traceback # For detailed error logs

# LangChain components for RAG
from langchain_community.document_loaders import UnstructuredFileLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate

# *** IMPORTANT: Using AZURE OpenAI for both Embeddings and LLM ***
# Make sure you have installed: pip install langchain-openai
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings


# Load environment variables from .env file
load_dotenv()

app = FastAPI()

# --- CORS Configuration ---
origins = [
    "http://localhost:3001", # Your React app's default address
    # If you changed your React frontend to a different port (e.g., 3001), update this:
    # "http://localhost:3001",
    # Add other origins if your frontend is hosted elsewhere
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- RAG Setup Variables ---
UPLOAD_DIRECTORY = "uploaded_files"
VECTOR_DB_DIRECTORY = "chroma_db" # Directory to store ChromaDB persistent data

# Global variables to hold the vector store and LLM instance
vectorstore = None
llm = None

# Ensure directories exist
os.makedirs(UPLOAD_DIRECTORY, exist_ok=True)
os.makedirs(VECTOR_DB_DIRECTORY, exist_ok=True)

# --- Helper Functions for RAG Pipeline ---

async def process_and_store_document(file_path: str):
    global vectorstore, llm # Declare intent to modify global variables

    print(f"Processing document: {file_path} for RAG...")

    # 1. Load Document
    # LangChainDeprecationWarning: UnstructuredFileLoader is deprecated.
    # If you want to fix this warning, you'd install `pip install -U langchain-unstructured`
    # and then change:
    # from langchain_community.document_loaders import UnstructuredFileLoader
    # to: from langchain_unstructured import UnstructuredLoader
    # and: loader = UnstructuredFileLoader(file_path)
    # to: loader = UnstructuredLoader(file_path)
    loader = UnstructuredFileLoader(file_path)
    documents = loader.load()
    print(f"Loaded {len(documents)} pages/chunks from {file_path}")

    # 2. Split Document into Chunks
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    texts = text_splitter.split_documents(documents)
    print(f"Split into {len(texts)} text chunks.")

    # 3. Create Embeddings using AzureOpenAIEmbeddings
    # Ensure all AZURE_OPENAI_... environment variables are set in your .env file.
    embeddings = AzureOpenAIEmbeddings(
        azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"), # Your embedding deployment name
        openai_api_version=os.getenv("AZURE_OPENAI_API_EMBEDDING_VERSION"),
        azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT"),
        openai_api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
        chunk_size=16 # Recommended for Azure embeddings
    )
    print("Azure OpenAI Embedding model initialized.")


    # 4. Store Embeddings in ChromaDB
    # If vectorstore is None or the directory doesn't contain a valid collection, create a new one.
    # Otherwise, load the existing one and add new documents.
    # 'index' directory inside VECTOR_DB_DIRECTORY is where Chroma stores its actual data
    if vectorstore is None or not os.path.exists(os.path.join(VECTOR_DB_DIRECTORY, "index")):
        print("Creating new ChromaDB collection.")
        vectorstore = Chroma.from_documents(
            documents=texts,
            embedding=embeddings,
            persist_directory=VECTOR_DB_DIRECTORY # Save to disk
        )
    else:
        print("Loading existing ChromaDB collection and adding new documents.")
        # Load existing collection
        existing_vectorstore = Chroma(
            persist_directory=VECTOR_DB_DIRECTORY,
            embedding_function=embeddings # IMPORTANT: must use the same embedding function as creation
        )
        existing_vectorstore.add_documents(texts)
        vectorstore = existing_vectorstore # Update the global reference

    # LangChainDeprecationWarning: Since Chroma 0.4.x the manual persistence method is no longer supported as docs are automatically persisted.
    # You can remove the line below if you wish, but it won't cause issues.
    vectorstore.persist()
    print("Vector store updated and persisted.")

    # Initialize LLM if not already done
    # Ensure all AZURE_OPENAI_... environment variables are set in your .env file.
    if llm is None:
        llm = AzureChatOpenAI(
            azure_deployment=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"), # Your chat model deployment name
            openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_CHAT_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_API_CHAT_KEY"),
            temperature=0.7 # Adjust for creativity (0.0 for deterministic, 1.0 for highly creative)
        )
        print(f"LLM initialized: {llm.model_name if hasattr(llm, 'model_name') else llm.__class__.__name__}")

# --- API Endpoints ---

@app.get("/")
async def root():
    return {"message": "FastAPI server is running!"}

@app.post("/upload")
async def upload_file_endpoint(file: UploadFile = File(...)):
    """
    Handles file uploads, saves them, and then processes them for RAG.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file name provided.")

    file_location = os.path.join(UPLOAD_DIRECTORY, file.filename)

    try:
        # Save the file
        with open(file_location, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        print(f"File '{file.filename}' saved successfully to {file_location}")

        # --- Trigger RAG Processing after saving ---
        await process_and_store_document(file_location)

        return JSONResponse(status_code=200, content={"message": f"File '{file.filename}' uploaded and processed for RAG!"})
    except Exception as e:
        print(f"Error during file upload or RAG processing for '{file.filename}': {e}")
        # Print full traceback for detailed debugging in development
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to process file: {e}")
    finally:
        # Ensure the uploaded file is closed
        file.file.close()


@app.post("/query")
async def process_query_endpoint(data: dict):
    """
    Receives a user query, retrieves relevant information, and generates a response.
    """
    query = data.get("query")
    if not query:
        raise HTTPException(status_code=400, detail="Query not provided.")

    global vectorstore, llm

    # Ensure vector store is loaded or initialized
    # IMPORTANT: Ensure the same embedding function is used to load ChromaDB
    if vectorstore is None and os.path.exists(os.path.join(VECTOR_DB_DIRECTORY, "index")):
        print("Loading existing ChromaDB collection for query...")
        embeddings = AzureOpenAIEmbeddings(
            azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_EMBEDDING_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
            chunk_size=16
        )
        vectorstore = Chroma(
            persist_directory=VECTOR_DB_DIRECTORY,
            embedding_function=embeddings
        )
        print("ChromaDB loaded.")
    elif vectorstore is None:
        return JSONResponse(status_code=404, content={"response": "No documents have been uploaded yet. Please upload a file first."})

    # Ensure LLM is initialized
    if llm is None:
        llm = AzureChatOpenAI(
            azure_deployment=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_CHAT_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_API_CHAT_KEY"),
            temperature=0.7
        )
        print(f"LLM re-initialized for query: {llm.model_name if hasattr(llm, 'model_name') else llm.__class__.__name__}")


    print(f"Received query: '{query}'. Retrieving relevant documents...")

    try:
        # Define a custom prompt to guide the LLM's response
        qa_template = """Use the following pieces of context to answer the question at the end.
        If you don't know the answer, just say that you don't know, don't try to make up an answer.
        Use three sentences maximum and keep the answer as concise as possible.
        Always say "Thanks for asking!" at the end of the answer.

        {context}

        Question: {question}
        Helpful Answer:"""
        QA_CHAIN_PROMPT = PromptTemplate.from_template(qa_template)

        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff", # "stuff" concatenates all retrieved documents into the prompt
            retriever=vectorstore.as_retriever(),
            return_source_documents=True, # Optional: to see which documents were used
            chain_type_kwargs={"prompt": QA_CHAIN_PROMPT} # Apply custom prompt
        )

        result = await qa_chain.ainvoke({"query": query}) # Use ainvoke for async operations

        response_text = result["result"]
        # print(f"Retrieved source documents: {result.get('source_documents')}") # For debugging purposes

        return JSONResponse(status_code=200, content={"response": response_text})

    except Exception as e:
        print(f"Error during query processing: {e}")
        traceback.print_exc() # Print full traceback for debugging
        return JSONResponse(status_code=500, content={"response": f"An error occurred during processing: {e}"})