from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from typing import List
import traceback

# Azure Blob Storage imports
from azure.storage.blob import BlobServiceClient

# LangChain components
from langchain_community.document_loaders import AzureBlobStorageFileLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain_core.prompts import PromptTemplate

# Azure OpenAI for LLM and Embeddings
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings

# Azure AI Search for Vector Store
from langchain_community.vectorstores import AzureSearch # Corrected to AzureSearch

# Load environment variables
load_dotenv()

app = FastAPI()

# --- CORS Configuration ---
origins = [
    "http://ragfrontend-nodeport",  # Frontend service in Kubernetes
    "http://ragfrontend-nodeport:3000",  # Frontend service with port
    "http://localhost:3000",  # For local development
    "http://127.0.0.1:3000",  # For local development
    # Add your cluster/ingress URLs here if needed
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- RAG Setup Variables ---
# Global variables to hold the vector store and LLM instance
vectorstore = None
llm = None
embeddings_model = None

# Azure Blob Storage client
blob_service_client = None



# --- Helper Functions ---

async def get_blob_service_client():
    """Initializes and returns the Azure Blob Service Client."""
    global blob_service_client
    if blob_service_client is None:
        try:
            account_name = os.getenv("AZURE_BLOB_STORAGE_ACCOUNT_NAME")
            account_key = os.getenv("AZURE_BLOB_STORAGE_ACCOUNT_KEY")

            if not account_name or not account_key:
                raise ValueError("Azure Blob Storage credentials not found in environment variables")

            account_url = f"https://{account_name}.blob.core.windows.net"
            blob_service_client = BlobServiceClient(account_url=account_url, credential=account_key)
            print("Azure Blob Service Client initialized.")
        except Exception as e:
            print(f"Error initializing Azure Blob Service Client: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to connect to Azure Blob Storage: {e}")
    return blob_service_client

async def upload_file_to_blob(file_name: str, file_content: bytes):
    """Uploads a file's content to Azure Blob Storage and returns the blob URL."""
    try:
        service_client = await get_blob_service_client()
        container_name = os.getenv("AZURE_BLOB_CONTAINER_NAME")

        if not container_name:
            raise ValueError("Azure Blob Container name not found in environment variables")

        # Get blob client
        blob_client = service_client.get_blob_client(container=container_name, blob=file_name)

        # Upload file content
        blob_client.upload_blob(file_content, overwrite=True)

        print(f"File '{file_name}' uploaded successfully to Azure Blob Storage container '{container_name}'.")
        return blob_client.url

    except Exception as e:
        print(f"Error uploading file '{file_name}' to Azure Blob Storage: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to upload file to Blob Storage: {e}")



async def initialize_llm_and_embeddings():
    """Initializes LLM and Embeddings if they are not already."""
    global llm, embeddings_model

    if llm is None:
        llm = AzureChatOpenAI(
            azure_deployment=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_CHAT_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_API_CHAT_KEY"),
            temperature=0.7
        )
        print(f"LLM initialized: {llm.model_name if hasattr(llm, 'model_name') else llm.__class__.__name__}")

    if embeddings_model is None:
        embeddings_model = AzureOpenAIEmbeddings(
            azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_EMBEDDING_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
            chunk_size=16
        )
        print("Azure OpenAI Embedding model initialized.")

async def get_azure_ai_search_vectorstore():
    """Initializes or retrieves the Azure AI Search vector store."""
    global vectorstore, embeddings_model

    if embeddings_model is None:
        await initialize_llm_and_embeddings()

    if vectorstore is None:
        print("Initializing Azure AI Search Vector Store...")
        try:
            # Use AzureSearch from langchain_community
            vectorstore = AzureSearch(
                azure_search_endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
                azure_search_key=os.getenv("AZURE_AI_SEARCH_API_KEY"),
                index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
                embedding_function=embeddings_model.embed_query,
            )
            print(f"Azure AI Search Vector Store initialized with index '{os.getenv('AZURE_AI_SEARCH_INDEX_NAME')}'.")
        except Exception as e:
            print(f"Error initializing Azure AI Search Vector Store: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to connect to Azure AI Search: {e}")
    return vectorstore


async def process_and_store_document_from_blob(blob_url: str, file_name: str):
    print(f"Processing document from Blob Storage: {blob_url} for RAG...")

    # Ensure LLM and Embeddings are initialized before processing
    await initialize_llm_and_embeddings()

    # 1. Load Document from Azure Blob Storage
    try:
        loader = AzureBlobStorageFileLoader(
            conn_str=f"DefaultEndpointsProtocol=https;AccountName={os.getenv('AZURE_BLOB_STORAGE_ACCOUNT_NAME')};AccountKey={os.getenv('AZURE_BLOB_STORAGE_ACCOUNT_KEY')};EndpointSuffix=core.windows.net",
            container=os.getenv("AZURE_BLOB_CONTAINER_NAME"),
            blob_name=file_name
        )
        documents = loader.load()
        print(f"Loaded {len(documents)} pages/chunks from {blob_url}")
    except Exception as e:
        print(f"Error loading document from blob storage: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to load document from blob storage: {e}")

    # 2. Split Document into Chunks
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    texts = text_splitter.split_documents(documents)
    print(f"Split into {len(texts)} text chunks.")

    # 3. Get the Azure AI Search Vector Store instance
    search_vectorstore = await get_azure_ai_search_vectorstore()

    # 4. Add documents to Azure AI Search
    print(f"Adding {len(texts)} chunks to Azure AI Search index '{os.getenv('AZURE_AI_SEARCH_INDEX_NAME')}'...")
    try:
        # Try to add documents to existing index
        result = search_vectorstore.add_documents(documents=texts)
        print(f"Documents successfully added to Azure AI Search. IDs: {result}")
        print("Note: Documents may take 1-2 seconds to become searchable due to Azure indexing delay.")
    except Exception as e:
        # This error handling for "not found" is specific to AzureSearch implementation.
        # It attempts to create the index if it doesn't exist on add.
        # The exact error message might vary based on the specific AzureSearch implementation.
        if "not found" in str(e).lower() or "ResourceNotFound" in str(e):
             print("Index not found or not initialized. Attempting to create new index with documents...")
             try:
                 # Create the index by using from_documents or from_texts method
                 # from_documents is preferred as it keeps metadata linked to documents
                 new_vectorstore = AzureSearch.from_documents(
                     documents=texts, # Pass LangChain Document objects directly
                     embedding=embeddings_model, # Pass the embedding model instance
                     azure_search_endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
                     azure_search_key=os.getenv("AZURE_AI_SEARCH_API_KEY"),
                     index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
                 )
                 global vectorstore
                 vectorstore = new_vectorstore
                 print("Index created and documents added successfully.")
                 print("Note: Documents may take 1-2 seconds to become searchable due to Azure indexing delay.")
             except Exception as create_error:
                 print(f"Error creating index: {create_error}")
                 traceback.print_exc()
                 raise HTTPException(status_code=500, detail=f"Failed to create Azure AI Search index: {create_error}")
        else:
            print(f"Error adding documents to Azure AI Search: {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to add documents to Azure AI Search: {e}")


# --- API Endpoints ---

@app.get("/")
async def root():
    return {"message": "FastAPI server is running!"}

@app.post("/upload")
async def upload_file_endpoint(file: UploadFile = File(...)):
    """
    Handles file uploads, uploads them to Azure Blob Storage,
    and then processes them for RAG from the blob URL.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file name provided.")

    try:
        # Read file content
        file_content = await file.read()

        # Upload file to Azure Blob Storage
        blob_url = await upload_file_to_blob(file.filename, file_content)

        # --- Trigger RAG Processing from Blob URL ---
        await process_and_store_document_from_blob(blob_url, file.filename)

        return JSONResponse(status_code=200, content={
            "message": f"File '{file.filename}' uploaded to Azure Blob Storage and processed for RAG!",
            "blob_url": blob_url
        })
    except Exception as e:
        print(f"Error during file upload to Blob or RAG processing for '{file.filename}': {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to process file: {e}")
    finally:
        # Ensure the uploaded file is closed
        file.file.close()


@app.post("/query")
async def process_query_endpoint(data: dict):
    query = data.get("query")
    if not query:
        raise HTTPException(status_code=400, detail="Query not provided.")

    global llm, embeddings_model

    await initialize_llm_and_embeddings()
    search_vectorstore = await get_azure_ai_search_vectorstore()

    print(f"Received query: '{query}'. Retrieving relevant documents from Azure AI Search...")

    try:
        qa_template = """Use the following pieces of context to answer the question at the end.
        If you don't know the answer, just say that you don't know, don't try to make up an answer.
        Use three sentences maximum and keep the answer as concise as possible.
        Always say "Thanks for asking!" at the end of the answer.

        {context}

        Question: {question}
        Helpful Answer:"""
        QA_CHAIN_PROMPT = PromptTemplate.from_template(qa_template)

        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=search_vectorstore.as_retriever(),
            return_source_documents=True,
            chain_type_kwargs={"prompt": QA_CHAIN_PROMPT}
        )

        result = await qa_chain.ainvoke({"query": query})
        response_text = result["result"]
        return JSONResponse(status_code=200, content={"response": response_text})

    except Exception as e:
        print(f"Error during query processing: {e}")
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"response": f"An error occurred during processing: {e}"})

# Run the application
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=5001)