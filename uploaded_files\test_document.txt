
    This is a test document for RAG implementation.
    
    The document contains some sample text that can be used to test
    the upload and processing functionality of the RAG system.
    
    Key points:
    1. Document upload should work
    2. Text should be processed and stored in vector database
    3. The system should be able to retrieve relevant information
    
    This is a simple test to verify the basic functionality.
    