apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: default
  labels:
    app: rag-backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 5001
    protocol: TCP
    name: http
  selector:
    app: rag-backend
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service-nodeport
  namespace: default
  labels:
    app: rag-backend
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 5001
    nodePort: 30001
    protocol: TCP
    name: http
  selector:
    app: rag-backend
