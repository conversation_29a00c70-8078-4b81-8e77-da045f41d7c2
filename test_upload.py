#!/usr/bin/env python3
"""
Test script to verify file upload functionality
"""
import requests
import os

def test_upload():
    # Create a test file
    test_content = """
    This is a test document for RAG implementation.
    
    The document contains some sample text that can be used to test
    the upload and processing functionality of the RAG system.
    
    Key points:
    1. Document upload should work
    2. Text should be processed and stored in vector database
    3. The system should be able to retrieve relevant information
    
    This is a simple test to verify the basic functionality.
    """
    
    # Save test file
    test_file_path = "test_document.txt"
    with open(test_file_path, "w") as f:
        f.write(test_content)
    
    print(f"Created test file: {test_file_path}")
    
    # Test upload
    url = "http://127.0.0.1:5000/upload"
    
    try:
        with open(test_file_path, "rb") as f:
            files = {"file": (test_file_path, f, "text/plain")}
            response = requests.post(url, files=files)
        
        print(f"Upload response status: {response.status_code}")
        print(f"Upload response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Upload successful!")
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
    
    # Clean up
    if os.path.exists(test_file_path):
        os.remove(test_file_path)
        print(f"Cleaned up test file: {test_file_path}")

if __name__ == "__main__":
    test_upload()
