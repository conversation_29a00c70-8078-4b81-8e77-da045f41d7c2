# Production-ready secrets for sensitive data
# Use this instead of storing sensitive data in ConfigMap

apiVersion: v1
kind: Secret
metadata:
  name: rag-backend-secrets
  namespace: default
type: Opaque
data:
  # Base64 encoded values - replace with your actual base64 encoded secrets
  # To encode: echo -n "your-secret-value" | base64
  
  # Azure OpenAI API Keys (base64 encoded)
  AZURE_OPENAI_API_CHAT_KEY: RGZEUGQ4Ykw4emZQWWZKdHFiSVQyUnNWU2JCQjQzZ3V5dVNXZVRHRjhQSU9WcVgzWnZrVUpRUUo5OUJEQUNZZUJqRlhKM3czQUFBQkFDT0dFQUNx
  AZURE_OPENAI_EMBEDDING_KEY: RGZEUGQ4Ykw4emZQWWZKdHFiSVQyUnNWU2JCQjQzZ3V5dVNXZVRHRjhQSU9WcVgzWnZrVUpRUUo5OUJEQUNZZUJqRlhKM3czQUFBQkFDT0dFQUNx
  
  # Azure AI Search API Key (base64 encoded)
  AZURE_AI_SEARCH_API_KEY: ************************************************************************
  
  # Azure Blob Storage Account Key (base64 encoded)
  AZURE_BLOB_STORAGE_ACCOUNT_KEY: ****************************************************************************************OUhud2JZRko2K0FTdFFLc2tlUT09

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-backend-config-prod
  namespace: default
data:
  # Non-sensitive configuration data
  # Azure OpenAI Configuration
  AZURE_OPENAI_CHAT_ENDPOINT: "https://idpchatbot.openai.azure.com/"
  AZURE_OPENAI_API_VERSION: "2024-12-01-preview"
  AZURE_OPENAI_CHAT_DEPLOYMENT_NAME: "gpt-4o"
  AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME: "text-embedding-ada-002"
  AZURE_OPENAI_EMBEDDING_ENDPOINT: "https://idpchatbot.openai.azure.com/"
  AZURE_OPENAI_API_EMBEDDING_VERSION: "2023-05-15"
  
  # Azure AI Search Configuration
  AZURE_AI_SEARCH_ENDPOINT: "https://idpgenaivectordb.search.windows.net"
  AZURE_AI_SEARCH_INDEX_NAME: "langchain-vector-demo"
  
  # Azure Blob Storage Configuration
  AZURE_BLOB_STORAGE_ACCOUNT_NAME: "idpgenai"
  AZURE_BLOB_CONTAINER_NAME: "ragfiles"
