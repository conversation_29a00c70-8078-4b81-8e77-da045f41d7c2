#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI configuration
"""
import os
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings

# Load environment variables
load_dotenv()

def test_azure_config():
    print("Testing Azure OpenAI Configuration...")
    
    # Print environment variables (without showing keys)
    print(f"AZURE_OPENAI_CHAT_ENDPOINT: {os.getenv('AZURE_OPENAI_CHAT_ENDPOINT')}")
    print(f"AZURE_OPENAI_API_VERSION: {os.getenv('AZURE_OPENAI_API_VERSION')}")
    print(f"AZURE_OPENAI_CHAT_DEPLOYMENT_NAME: {os.getenv('AZURE_OPENAI_CHAT_DEPLOYMENT_NAME')}")
    print(f"AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME: {os.getenv('AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME')}")
    print(f"AZURE_OPENAI_EMBEDDING_ENDPOINT: {os.getenv('AZURE_OPENAI_EMBEDDING_ENDPOINT')}")
    print(f"AZURE_OPENAI_API_EMBEDDING_VERSION: {os.getenv('AZURE_OPENAI_API_EMBEDDING_VERSION')}")
    
    # Check if keys are loaded
    chat_key = os.getenv('AZURE_OPENAI_API_CHAT_KEY')
    embedding_key = os.getenv('AZURE_OPENAI_EMBEDDING_KEY')
    
    print(f"Chat key loaded: {'Yes' if chat_key else 'No'}")
    print(f"Embedding key loaded: {'Yes' if embedding_key else 'No'}")
    
    try:
        # Test embedding initialization
        print("\nTesting Azure OpenAI Embeddings...")
        embeddings = AzureOpenAIEmbeddings(
            azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_EMBEDDING_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
            chunk_size=16
        )
        print("✓ Embeddings initialized successfully")
        
        # Test a simple embedding
        test_text = ["Hello world"]
        result = embeddings.embed_documents(test_text)
        print(f"✓ Embedding test successful - got {len(result)} embeddings")
        
    except Exception as e:
        print(f"✗ Embedding test failed: {e}")
        return False
    
    try:
        # Test chat model initialization
        print("\nTesting Azure OpenAI Chat...")
        llm = AzureChatOpenAI(
            azure_deployment=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_CHAT_ENDPOINT"),
            openai_api_key=os.getenv("AZURE_OPENAI_API_CHAT_KEY"),
            temperature=0.7
        )
        print("✓ Chat model initialized successfully")
        
        # Test a simple chat
        response = llm.invoke("Say hello")
        print(f"✓ Chat test successful - response: {response.content[:50]}...")
        
    except Exception as e:
        print(f"✗ Chat test failed: {e}")
        return False
    
    print("\n✓ All Azure OpenAI tests passed!")
    return True

if __name__ == "__main__":
    test_azure_config()
