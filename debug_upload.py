#!/usr/bin/env python3
"""
Debug script to test upload and monitor what happens during the process
"""
import requests
import os
import time

def debug_upload():
    """Upload a test file and monitor the process"""
    
    # Create a simple test file
    test_content = """
    Debug Test Document for RAG System
    
    This is a test document to debug the embedding storage issue.
    
    Key information:
    - Company: DebugTech Solutions
    - Founded: 2024
    - Location: Test City
    - Purpose: Debugging RAG implementation
    
    This document should be processed, chunked, and stored in the vector database.
    If you can search for "DebugTech" later, then the storage is working.
    """
    
    test_file_path = "debug_test.txt"
    
    try:
        # Create test file
        with open(test_file_path, "w") as f:
            f.write(test_content)
        print(f"✅ Created test file: {test_file_path}")
        
        # Upload the file
        url = "http://127.0.0.1:5000/upload"
        print(f"🚀 Uploading to: {url}")
        
        with open(test_file_path, "rb") as f:
            files = {"file": (test_file_path, f, "text/plain")}
            
            print("📤 Sending upload request...")
            start_time = time.time()
            response = requests.post(url, files=files)
            end_time = time.time()
            
            print(f"⏱️  Upload took: {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            print(f"📝 Response content: {response.text}")
            
            if response.status_code == 200:
                print("✅ Upload returned 200 - SUCCESS")
                
                # Wait a moment for processing
                print("⏳ Waiting 5 seconds for processing to complete...")
                time.sleep(5)
                
                # Now test if we can query the uploaded content
                print("🔍 Testing query functionality...")
                test_query_uploaded_content()
                
            else:
                print(f"❌ Upload failed with status: {response.status_code}")
                print(f"Error details: {response.text}")
                
    except Exception as e:
        print(f"❌ Upload error: {e}")
        
    finally:
        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"🧹 Cleaned up: {test_file_path}")

def test_query_uploaded_content():
    """Test if we can query the content we just uploaded"""
    
    query_url = "http://127.0.0.1:5000/query"
    
    test_queries = [
        "What is DebugTech Solutions?",
        "When was DebugTech founded?",
        "What is the purpose of this debug document?"
    ]
    
    for query in test_queries:
        print(f"\n🤖 Testing query: '{query}'")
        
        try:
            response = requests.post(query_url, json={"query": query})
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('response', 'No response')
                print(f"  ✅ Answer: {answer[:200]}...")
                
                # Check if the answer contains relevant information
                if "DebugTech" in answer or "2024" in answer or "debug" in answer.lower():
                    print("  🎯 Answer contains relevant information - EMBEDDINGS WORKING!")
                else:
                    print("  ⚠️  Answer doesn't contain expected information - possible embedding issue")
                    
            else:
                print(f"  ❌ Query failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"  ❌ Query error: {e}")

def check_vector_store_directly():
    """Check the vector store directly using Azure Search"""
    
    print("\n🔍 Checking vector store directly...")
    
    try:
        from dotenv import load_dotenv
        from azure.search.documents import SearchClient
        from azure.core.credentials import AzureKeyCredential
        
        load_dotenv()
        
        search_client = SearchClient(
            endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
            index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
            credential=AzureKeyCredential(os.getenv("AZURE_AI_SEARCH_API_KEY"))
        )
        
        # Search for our debug content
        results = search_client.search(search_text="DebugTech", top=5)
        
        found_count = 0
        for result in results:
            found_count += 1
            content = result.get('content', '')[:150]
            print(f"  📄 Found: {content}...")
            
        if found_count > 0:
            print(f"  ✅ Found {found_count} documents with 'DebugTech' - STORAGE WORKING!")
        else:
            print("  ❌ No documents found with 'DebugTech' - STORAGE NOT WORKING!")
            
    except Exception as e:
        print(f"  ❌ Direct vector store check failed: {e}")

if __name__ == "__main__":
    print("🐛 DEBUG: Testing upload and embedding storage...")
    print("=" * 60)
    
    # Test upload
    debug_upload()
    
    # Check vector store directly
    check_vector_store_directly()
    
    print("\n" + "=" * 60)
    print("🐛 Debug test complete!")
