# RAG Backend Kubernetes Deployment

This document provides instructions for deploying the RAG Backend service to Kubernetes.

## 📋 Prerequisites

- Docker installed and running
- kubectl configured to connect to your Kubernetes cluster
- Kubernetes cluster with sufficient resources

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Azure         │
│ ragfrontend-    │───▶│ backend-service │───▶│   Services      │
│ nodeport        │    │ (Port 80)       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │ rag-backend-    │
                       │ deployment      │
                       │ (2 replicas)    │
                       └─────────────────┘
```

## 📦 Components

### 1. ConfigMap (`k8s-configmap.yaml`)
- Stores all environment variables from `.env` file
- Contains Azure OpenAI, Azure AI Search, and Azure Blob Storage credentials
- **⚠️ Security Note**: In production, use Kubernetes Secrets instead of ConfigMap for sensitive data

### 2. Deployment (`k8s-deployment.yaml`)
- Runs 2 replicas of the RAG backend
- Uses the ConfigMap for environment variables
- Includes health checks (liveness and readiness probes)
- Resource limits: 1Gi memory, 500m CPU

### 3. Service (`k8s-service.yaml`)
- **ClusterIP Service**: `backend-service` (internal cluster access)
- **NodePort Service**: `backend-service-nodeport` (external access on port 30001)

## 🚀 Quick Deployment

### Option 1: Using the deployment script
```bash
chmod +x deploy.sh
./deploy.sh
```

### Option 2: Manual deployment
```bash
# Build Docker image
docker build -t rag-backend:latest .

# Apply Kubernetes manifests
kubectl apply -f k8s-configmap.yaml
kubectl apply -f k8s-deployment.yaml
kubectl apply -f k8s-service.yaml

# Wait for deployment
kubectl wait --for=condition=available --timeout=300s deployment/rag-backend-deployment
```

## 🔍 Verification

### Check deployment status
```bash
kubectl get pods -l app=rag-backend
kubectl get services backend-service backend-service-nodeport
kubectl get configmap rag-backend-config
```

### View logs
```bash
kubectl logs -l app=rag-backend -f
```

### Test the service
```bash
# Port forward to test locally
kubectl port-forward service/backend-service 5001:80

# Test health endpoint
curl http://localhost:5001/
```

## 🌐 Service Access

### Internal (from other pods in cluster)
```
http://backend-service/
http://backend-service.default.svc.cluster.local/
```

### External (NodePort)
```
http://<node-ip>:30001/
```

## 🔧 Configuration

### Environment Variables
All environment variables are stored in the ConfigMap `rag-backend-config`:

- **Azure OpenAI**: Endpoints, API keys, deployment names
- **Azure AI Search**: Endpoint, API key, index name  
- **Azure Blob Storage**: Account name, key, container name

### CORS Configuration
The backend is configured to accept requests from:
- `http://ragfrontend-nodeport` (Kubernetes service)
- `http://ragfrontend-nodeport:3000`
- `http://localhost:3000` (local development)

## 📊 Monitoring

### Health Checks
- **Liveness Probe**: Checks if the container is running
- **Readiness Probe**: Checks if the container is ready to serve traffic
- Both probes use the `/` endpoint

### Resource Usage
```bash
kubectl top pods -l app=rag-backend
```

## 🔄 Updates

### Update the application
```bash
# Build new image
docker build -t rag-backend:latest .

# Restart deployment to pull new image
kubectl rollout restart deployment/rag-backend-deployment

# Check rollout status
kubectl rollout status deployment/rag-backend-deployment
```

### Update configuration
```bash
# Edit the ConfigMap
kubectl edit configmap rag-backend-config

# Restart deployment to pick up changes
kubectl rollout restart deployment/rag-backend-deployment
```

## 🗑️ Cleanup

```bash
kubectl delete -f k8s-service.yaml
kubectl delete -f k8s-deployment.yaml
kubectl delete -f k8s-configmap.yaml
```

## 🔒 Security Considerations

### For Production:
1. **Use Secrets instead of ConfigMap** for sensitive data:
   ```bash
   kubectl create secret generic rag-backend-secrets \
     --from-literal=AZURE_OPENAI_API_CHAT_KEY="your-key" \
     --from-literal=AZURE_AI_SEARCH_API_KEY="your-key"
   ```

2. **Enable RBAC** and create service accounts with minimal permissions

3. **Use network policies** to restrict pod-to-pod communication

4. **Enable pod security standards**

5. **Use private container registry**

## 🐛 Troubleshooting

### Pod not starting
```bash
kubectl describe pod -l app=rag-backend
kubectl logs -l app=rag-backend
```

### Service not accessible
```bash
kubectl describe service backend-service
kubectl get endpoints backend-service
```

### Configuration issues
```bash
kubectl describe configmap rag-backend-config
kubectl exec -it deployment/rag-backend-deployment -- env | grep AZURE
```

## 📞 Support

For issues related to:
- **Kubernetes deployment**: Check pod logs and events
- **Azure services**: Verify credentials and service availability
- **Application errors**: Check application logs in the pods
