apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-backend-deployment
  namespace: default
  labels:
    app: rag-backend
    version: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rag-backend
  template:
    metadata:
      labels:
        app: rag-backend
        version: production
    spec:
      containers:
      - name: rag-backend
        image: rag-backend:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5001
          name: http
        env:
        # Non-sensitive config from ConfigMap
        - name: AZURE_OPENAI_CHAT_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_CHAT_ENDPOINT
        - name: AZURE_OPENAI_API_VERSION
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_API_VERSION
        - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME
        - name: AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME
        - name: AZURE_OPENAI_EMBEDDING_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_EMBEDDING_ENDPOINT
        - name: AZURE_OPENAI_API_EMBEDDING_VERSION
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_OPENAI_API_EMBEDDING_VERSION
        - name: AZURE_AI_SEARCH_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_AI_SEARCH_ENDPOINT
        - name: AZURE_AI_SEARCH_INDEX_NAME
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_AI_SEARCH_INDEX_NAME
        - name: AZURE_BLOB_STORAGE_ACCOUNT_NAME
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_BLOB_STORAGE_ACCOUNT_NAME
        - name: AZURE_BLOB_CONTAINER_NAME
          valueFrom:
            configMapKeyRef:
              name: rag-backend-config-prod
              key: AZURE_BLOB_CONTAINER_NAME
        # Sensitive data from Secrets
        - name: AZURE_OPENAI_API_CHAT_KEY
          valueFrom:
            secretKeyRef:
              name: rag-backend-secrets
              key: AZURE_OPENAI_API_CHAT_KEY
        - name: AZURE_OPENAI_EMBEDDING_KEY
          valueFrom:
            secretKeyRef:
              name: rag-backend-secrets
              key: AZURE_OPENAI_EMBEDDING_KEY
        - name: AZURE_AI_SEARCH_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-backend-secrets
              key: AZURE_AI_SEARCH_API_KEY
        - name: AZURE_BLOB_STORAGE_ACCOUNT_KEY
          valueFrom:
            secretKeyRef:
              name: rag-backend-secrets
              key: AZURE_BLOB_STORAGE_ACCOUNT_KEY
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 5001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 5001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      restartPolicy: Always
      securityContext:
        fsGroup: 1000
