#!/usr/bin/env python3
"""
Comprehensive verification of the RAG system to show exactly what's stored
"""
import os
from dotenv import load_dotenv
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
import requests
from datetime import datetime

load_dotenv()

def show_all_stored_documents():
    """Show all documents currently stored in the vector database"""
    
    print("🔍 COMPREHENSIVE VECTOR DATABASE VERIFICATION")
    print("=" * 60)
    
    try:
        search_client = SearchClient(
            endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
            index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
            credential=AzureKeyCredential(os.getenv("AZURE_AI_SEARCH_API_KEY"))
        )
        
        # Get all documents
        print("📊 Fetching ALL documents from Azure AI Search...")
        results = search_client.search(
            search_text="*",  # Get everything
            top=100,  # Get up to 100 documents
            include_total_count=True
        )
        
        documents_by_source = {}
        total_count = 0
        
        for result in results:
            total_count += 1
            source = result.get('metadata', {})
            if isinstance(source, str):
                import json
                try:
                    source = json.loads(source)
                except:
                    source = {'source': source}
            
            source_file = source.get('source', 'Unknown')
            
            if source_file not in documents_by_source:
                documents_by_source[source_file] = []
            
            documents_by_source[source_file].append({
                'id': result.get('id', 'N/A'),
                'content': result.get('content', '')[:150] + '...',
                'score': result.get('@search.score', 0)
            })
        
        print(f"\n📈 SUMMARY:")
        print(f"Total documents in vector database: {total_count}")
        print(f"Number of source files: {len(documents_by_source)}")
        
        print(f"\n📁 DOCUMENTS BY SOURCE FILE:")
        for source_file, docs in documents_by_source.items():
            print(f"\n📄 {source_file}")
            print(f"   Chunks: {len(docs)}")
            for i, doc in enumerate(docs[:3]):  # Show first 3 chunks
                print(f"   {i+1}. {doc['content']}")
            if len(docs) > 3:
                print(f"   ... and {len(docs) - 3} more chunks")
        
        return total_count > 0
        
    except Exception as e:
        print(f"❌ Error accessing vector database: {e}")
        return False

def test_upload_and_immediate_verification():
    """Upload a new document and immediately verify it's stored"""
    
    print(f"\n🧪 TESTING UPLOAD AND IMMEDIATE STORAGE")
    print("=" * 60)
    
    # Create a unique test document
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_content = f"""
    VERIFICATION TEST DOCUMENT - {timestamp}
    
    This is a unique test document created at {datetime.now()}.
    
    Test Information:
    - Test ID: VERIFY_{timestamp}
    - Purpose: Verify immediate storage in vector database
    - Content: This document should appear in the vector database immediately after upload
    
    If you can find this document with the test ID "VERIFY_{timestamp}", 
    then the embedding storage is working correctly.
    """
    
    test_file = f"verify_test_{timestamp}.txt"
    
    try:
        # Create and upload the test file
        with open(test_file, "w") as f:
            f.write(test_content)
        
        print(f"📝 Created test file: {test_file}")
        
        # Upload via API
        url = "http://127.0.0.1:5000/upload"
        with open(test_file, "rb") as f:
            files = {"file": (test_file, f, "text/plain")}
            response = requests.post(url, files=files)
        
        print(f"📤 Upload response: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Upload successful!")
            
            # Wait a moment
            import time
            print("⏳ Waiting 3 seconds for processing...")
            time.sleep(3)
            
            # Now search for our specific test document
            search_client = SearchClient(
                endpoint=os.getenv("AZURE_AI_SEARCH_ENDPOINT"),
                index_name=os.getenv("AZURE_AI_SEARCH_INDEX_NAME"),
                credential=AzureKeyCredential(os.getenv("AZURE_AI_SEARCH_API_KEY"))
            )
            
            search_term = f"VERIFY_{timestamp}"
            print(f"🔍 Searching for: {search_term}")
            
            results = search_client.search(search_text=search_term, top=5)
            
            found = False
            for result in results:
                found = True
                content = result.get('content', '')
                print(f"✅ FOUND: {content[:200]}...")
                break
            
            if found:
                print("🎉 SUCCESS: Document was uploaded and stored in vector database!")
            else:
                print("❌ ISSUE: Document was uploaded but not found in vector database!")
                
            # Test query endpoint
            print(f"\n🤖 Testing query endpoint...")
            query_response = requests.post(
                "http://127.0.0.1:5000/query", 
                json={"query": f"What is the test ID in the verification document?"}
            )
            
            if query_response.status_code == 200:
                answer = query_response.json().get('response', '')
                if timestamp in answer:
                    print(f"✅ Query endpoint working: {answer[:150]}...")
                else:
                    print(f"⚠️  Query endpoint response doesn't contain expected content: {answer[:150]}...")
            else:
                print(f"❌ Query endpoint failed: {query_response.status_code}")
                
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🧹 Cleaned up: {test_file}")

def main():
    print("🔬 COMPREHENSIVE RAG SYSTEM VERIFICATION")
    print(f"Time: {datetime.now()}")
    print("=" * 60)
    
    # Step 1: Show what's currently stored
    has_documents = show_all_stored_documents()
    
    # Step 2: Test upload and immediate verification
    test_upload_and_immediate_verification()
    
    # Step 3: Final summary
    print(f"\n📋 FINAL ASSESSMENT")
    print("=" * 60)
    
    if has_documents:
        print("✅ Vector database contains documents")
        print("✅ Embeddings are being stored successfully")
        print("✅ RAG system is working correctly")
        print("\n💡 If you're not seeing expected results, check:")
        print("   - Are you searching for terms that exist in your documents?")
        print("   - Are you uploading the files you think you are?")
        print("   - Are there multiple versions of the same document?")
    else:
        print("❌ No documents found in vector database")
        print("❌ There may be an issue with embedding storage")
        print("\n🔧 Troubleshooting needed:")
        print("   - Check Azure AI Search configuration")
        print("   - Verify embedding model is working")
        print("   - Check for error messages in server logs")

if __name__ == "__main__":
    main()
