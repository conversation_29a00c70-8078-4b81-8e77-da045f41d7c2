
    Debug Test Document for RAG System
    
    This is a test document to debug the embedding storage issue.
    
    Key information:
    - Company: DebugTech Solutions
    - Founded: 2024
    - Location: Test City
    - Purpose: Debugging RAG implementation
    
    This document should be processed, chunked, and stored in the vector database.
    If you can search for "DebugTech" later, then the storage is working.
    