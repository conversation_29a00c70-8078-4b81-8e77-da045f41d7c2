
    This is a test document for RAG implementation with index creation.
    
    The document contains sample text to test the upload functionality
    and automatic index creation in Azure AI Search.
    
    Key features being tested:
    1. Document upload and processing
    2. Automatic index creation if it doesn't exist
    3. Text chunking and embedding
    4. Storage in Azure AI Search vector database
    
    This test verifies that the system can handle the case where
    the Azure Search index doesn't exist initially.
    