#!/bin/bash

# RAG Backend Kubernetes Deployment Script
echo "🚀 Deploying RAG Backend to Kubernetes"
echo "======================================="

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t rag-backend:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully"
else
    echo "❌ Docker build failed"
    exit 1
fi

# Apply Kubernetes manifests
echo "📦 Applying Kubernetes manifests..."

# Apply ConfigMap
echo "   📋 Applying ConfigMap..."
kubectl apply -f k8s-configmap.yaml

# Apply Deployment
echo "   🚀 Applying Deployment..."
kubectl apply -f k8s-deployment.yaml

# Apply Service
echo "   🌐 Applying Service..."
kubectl apply -f k8s-service.yaml

# Wait for deployment to be ready
echo "⏳ Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/rag-backend-deployment

# Check deployment status
echo "📊 Checking deployment status..."
kubectl get pods -l app=rag-backend
kubectl get services backend-service backend-service-nodeport

echo ""
echo "🎉 Deployment completed!"
echo "======================================="
echo "📋 Service Information:"
echo "   • ClusterIP Service: backend-service (port 80)"
echo "   • NodePort Service: backend-service-nodeport (port 30001)"
echo ""
echo "🔍 To check logs:"
echo "   kubectl logs -l app=rag-backend -f"
echo ""
echo "🧪 To test the service:"
echo "   kubectl port-forward service/backend-service 5001:80"
echo "   curl http://localhost:5001/"
echo ""
echo "🗑️  To delete deployment:"
echo "   kubectl delete -f k8s-service.yaml"
echo "   kubectl delete -f k8s-deployment.yaml"
echo "   kubectl delete -f k8s-configmap.yaml"
