# Simple Dockerfile with better error handling
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip first
RUN pip install --upgrade pip

# Copy requirements and install Python dependencies
COPY requirements.txt .

# Install dependencies one by one to better identify issues
RUN pip install fastapi==0.104.1
RUN pip install uvicorn[standard]==0.24.0
RUN pip install python-multipart==0.0.6
RUN pip install python-dotenv==1.0.0
RUN pip install azure-storage-blob==12.26.0
RUN pip install azure-search-documents==11.6.0
RUN pip install azure-identity==1.15.0
RUN pip install langchain==0.1.0
RUN pip install langchain-community==0.0.10
RUN pip install langchain-openai==0.0.5
RUN pip install openai==1.6.1
RUN pip install unstructured==0.11.8
RUN pip install pydantic==2.5.2
RUN pip install requests==2.31.0
RUN pip install typing-extensions==4.9.0
RUN pip install tiktoken==0.5.2

# Copy application code
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/ || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5001"]
