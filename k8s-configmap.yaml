apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-backend-config
  namespace: default
data:
  # Azure OpenAI Configuration
  AZURE_OPENAI_CHAT_ENDPOINT: "https://idpchatbot.openai.azure.com/"
  AZURE_OPENAI_API_CHAT_KEY: "DfDPd8bL8zfPYfJtqbIT2RsVSbBB43guyuSWeTGF8PIOVqX3ZvkUJQQJ99BDACYeBjFXJ3w3AAABACOGEACq"
  AZURE_OPENAI_API_VERSION: "2024-12-01-preview"
  AZURE_OPENAI_CHAT_DEPLOYMENT_NAME: "gpt-4o"
  AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME: "text-embedding-ada-002"
  AZURE_OPENAI_EMBEDDING_ENDPOINT: "https://idpchatbot.openai.azure.com/"
  AZURE_OPENAI_EMBEDDING_KEY: "DfDPd8bL8zfPYfJtqbIT2RsVSbBB43guyuSWeTGF8PIOVqX3ZvkUJQQJ99BDACYeBjFXJ3w3AAABACOGEACq"
  AZURE_OPENAI_API_EMBEDDING_VERSION: "2023-05-15"
  
  # Azure AI Search Configuration
  AZURE_AI_SEARCH_ENDPOINT: "https://idpgenaivectordb.search.windows.net"
  AZURE_AI_SEARCH_API_KEY: "****************************************************"
  AZURE_AI_SEARCH_INDEX_NAME: "langchain-vector-demo"
  
  # Azure Blob Storage Configuration
  AZURE_BLOB_STORAGE_ACCOUNT_NAME: "idpgenai"
  AZURE_BLOB_STORAGE_ACCOUNT_KEY: "****************************************************************************************"
  AZURE_BLOB_CONTAINER_NAME: "ragfiles"
