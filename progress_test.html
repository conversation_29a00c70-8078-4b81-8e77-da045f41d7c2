<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Upload with Progress</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 RAG Upload with Real-time Progress</h1>
        
        <div class="upload-area">
            <input type="file" id="fileInput" accept=".pdf,.txt,.doc,.docx" style="display: none;">
            <div onclick="document.getElementById('fileInput').click()">
                <h3>📁 Click to Select File</h3>
                <p>Supports PDF, TXT, DOC, DOCX files</p>
            </div>
        </div>
        
        <button id="uploadBtn" class="btn" disabled>Upload & Process</button>
        
        <div class="progress-container" id="progressContainer">
            <h3>Processing Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Ready to start...</div>
            
            <h4>Status Log:</h4>
            <div class="status-log" id="statusLog"></div>
        </div>
    </div>

    <script>
        let websocket = null;
        let clientId = null;
        let selectedFile = null;

        // Generate unique client ID
        function generateClientId() {
            return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        }

        // Initialize WebSocket connection
        function initWebSocket() {
            clientId = generateClientId();
            websocket = new WebSocket(`ws://127.0.0.1:5000/ws/${clientId}`);
            
            websocket.onopen = function(event) {
                console.log('WebSocket connected');
                addToLog('🔗 Connected to server');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateProgress(data);
            };
            
            websocket.onclose = function(event) {
                console.log('WebSocket disconnected');
                addToLog('❌ Disconnected from server');
            };
            
            websocket.onerror = function(error) {
                console.error('WebSocket error:', error);
                addToLog('❌ Connection error');
            };
        }

        // Update progress based on WebSocket message
        function updateProgress(data) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            // Update progress bar
            progressFill.style.width = data.progress + '%';
            
            // Update progress text
            progressText.textContent = `${data.progress}% - ${data.message}`;
            
            // Add to log
            addToLog(`📊 ${data.step}: ${data.message} (${data.progress}%)`);
            
            // Handle completion
            if (data.status === 'completed') {
                addToLog('✅ Upload and processing completed successfully!');
                progressText.innerHTML = '<span class="success">✅ Processing Complete!</span>';
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('uploadBtn').textContent = 'Upload Another File';
            }
        }

        // Add message to status log
        function addToLog(message) {
            const statusLog = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            statusLog.innerHTML += `[${timestamp}] ${message}\n`;
            statusLog.scrollTop = statusLog.scrollHeight;
        }

        // Handle file selection
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (selectedFile) {
                uploadBtn.disabled = false;
                uploadBtn.textContent = `Upload "${selectedFile.name}"`;
                addToLog(`📄 Selected file: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        // Handle upload
        document.getElementById('uploadBtn').addEventListener('click', async function() {
            if (!selectedFile) return;
            
            // Show progress container
            document.getElementById('progressContainer').style.display = 'block';
            
            // Reset progress
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = 'Starting upload...';
            document.getElementById('statusLog').innerHTML = '';
            
            // Disable upload button
            this.disabled = true;
            this.textContent = 'Processing...';
            
            // Initialize WebSocket if not connected
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                initWebSocket();
                // Wait a moment for connection
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('client_id', clientId);
            
            addToLog('🚀 Starting upload...');
            
            try {
                const response = await fetch('http://127.0.0.1:5000/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addToLog('✅ Upload request successful');
                } else {
                    throw new Error(`Upload failed: ${response.statusText}`);
                }
            } catch (error) {
                addToLog(`❌ Upload error: ${error.message}`);
                document.getElementById('progressText').innerHTML = '<span class="error">❌ Upload Failed</span>';
                this.disabled = false;
                this.textContent = 'Try Again';
            }
        });

        // Initialize WebSocket on page load
        window.addEventListener('load', function() {
            initWebSocket();
        });
    </script>
</body>
</html>
